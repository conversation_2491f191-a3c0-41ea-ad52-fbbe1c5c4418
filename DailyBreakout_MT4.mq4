//+------------------------------------------------------------------+
//|                                           DailyBreakout_MT4.mq4 |
//|                                    Daily Breakout Master Bot MT4 |
//|                          BY BRIAN ALVIN BAGOROGOZA               |
//|                          +49 1521 6294394                       |
//|                          INNOVATIONX INTERNATIONAL              |
//+------------------------------------------------------------------+
#property copyright "<PERSON> - InnovationX International"
#property link      "https://innovationx-international.com"
#property version   "1.00"
#property description "Daily Breakout Master Strategy - Professional Trading Bot"
#property description "Contact: +49 1521 6294394"
#property description "Developed by <PERSON>"
#property strict

//--- Enums (must be declared before inputs)
enum ENUM_ENTRY_MODE {
   ENTRY_BODY, // Use body high/low for entry
   ENTRY_WICK  // Use wick high/low for entry
};

enum ENUM_SL_MODE {
   SL_RANGE_LOW,    // BUY SL = range low, SELL SL = range high
   SL_MID_RANGE,    // SL at middle of range
   SL_PERCENT,      // Use percentage of range
   SL_POINTS        // Use points from entry
};

enum ENUM_TP_TYPE {
   TP_FIXED_RR,     // Fixed Risk:Reward
   TP_ATR_BASED,    // ATR Based
   TP_CUSTOM_POINTS // Custom Points
};

enum ENUM_GAP_MODE {
   GAP_SKIP,        // Skip trading on gap days (safest)
   GAP_REVERSE,     // Trade reversals only (gap up = sell only, gap down = buy only)
   GAP_ADJUST       // Adjust levels to gap open (most aggressive)
};

enum ENUM_LOT_TYPE {
   LOT_MANUAL,      // Manual Lot Size
   LOT_RISK_BASED   // Risk % Based
};

//|======================== INPUT PARAMETERS =======================|
//|------------------------ MONEY MANAGEMENT ----------------------|
input string SECTION_1 = "--- Lot Size & Risk ---";
input group "=== LOT SIZE & RISK ==="
input ENUM_LOT_TYPE LotSizeType = LOT_RISK_BASED; // Lot Size Type
input double ManualLotSize = 0.01; // Manual Lot Size
input double RiskPercentPerTrade = 1.0; // Risk % Per Trade
input double MaxRiskPerWeek = 5.0; // Max Risk % Per Week

//|------------------------ TRADE SETTINGS -----------------------|
input string SECTION_2 = "--- Trade Settings ---";
input group "=== TRADE SETTINGS ==="
input int MaxDailyTrades = 1; // Max Trades Per Day (Each Direction)
input double MaxSpread = 3.0; // Max Spread (Pips)
input int Slippage = 2; // Max Slippage (Pips)
int MagicNumber = 12345; // Magic Number (non-const for auto-generation)
input bool AutoMagicNumber = true; // Auto-generate magic number per symbol+timeframe+account

input string TradeComment = "DailyBreakout"; // Trade Comment

//|------------------------ TAKE PROFIT SETUP --------------------|
input string SECTION_3 = "--- Take Profit Settings ---";
input group "=== TAKE PROFIT SETTINGS ==="
input ENUM_TP_TYPE TakeProfitType = TP_CUSTOM_POINTS; // Take Profit Type (Default ON, 1000 points)
input double FixedRiskReward = 2.0; // Fixed Risk:Reward Ratio
input double ATRMultiplier = 2.0; // ATR Multiplier for TP
input int ATRPeriod = 14; // ATR Period
input double CustomTPPoints = 1000.0; // Custom TP (Points) - Default 1000 for indices/gold/BTC

//|------------------------ RISK MANAGEMENT ----------------------|
input string SECTION_4 = "--- Stop Loss & Protection ---";
input group "=== PROTECTION & FILTERS ==="
input bool EnableEquityGuard = true; // Enable Equity Guard
input double EquityGuardPercent = 10.0; // Stop if Equity Drops Below %
input double MaxDrawdownPercent = 15.0; // Max Drawdown %
input double MaxDailyLossPercent = 4.0; // Max Daily Loss %
input double MaxWeeklyLossPercent = 8.0; // Max Weekly Loss %
input double MaxMonthlyLossPercent = 20.0; // Max Monthly Loss %
input bool EnableGapFilter = true; // Enable Gap Protection
input ENUM_GAP_MODE GapMode = GAP_SKIP; // Gap handling mode
input double MinGapSize = 20; // Minimum gap size to trigger protection (points)
input bool EnableNewsFilter = false; // Enable News Filter
input int NewsFilterMinutes = 30; // News Filter Minutes

//|------------------------ TRADE MANAGEMENT ----------------------|
input string SECTION_5 = "--- Trade Management ---";
input group "=== TRADE MANAGEMENT ==="
input bool EnableBreakEven = true; // Enable Break Even
input double BreakEvenPoints = 300.0; // Break Even trigger (points). Default 300

input bool EnableTrailingStop = true; // Enable Trailing Stop (Default ON)
input double TrailingStopPoints = 200.0; // Trailing Stop Distance (Points) - Default 200
input double TrailingStepPoints = 50.0; // Trailing Step (Points) - Default 50
input bool EnablePartialTP = false; // Enable Partial Take Profit
input double PartialTPPercent = 50.0; // Partial TP % at 1:1
input double PartialTP2Percent = 25.0; // Second Partial TP %
input double PartialTP2RR = 2.0; // Second Partial TP at X:1 RR

//|------------------------ TIME MANAGEMENT ----------------------|
input string SECTION_6 = "--- Time & Session Filters ---";
input group "=== TIME & SESSION FILTERS ==="
input bool EnableMondayTrading = true; // Trade on Monday
input bool EnableTuesdayTrading = true; // Trade on Tuesday
input bool EnableWednesdayTrading = true; // Trade on Wednesday
input bool EnableThursdayTrading = true; // Trade on Thursday
input bool EnableFridayTrading = true; // Trade on Friday
input int TriggerHour = 0; // Daily Candle Close Hour (Midnight broker time = 11 PM Berlin)
input int TriggerMinute = 0; // Daily Candle Close Minute (Broker Time)
input int SessionStartHour = 9; // Session Start Hour (9 AM broker time = 8 AM Berlin)
input int SessionEndHour = 18; // Session End Hour (6 PM broker time = 5 PM Berlin)
input int OrderPurgeHour = 21; // Delete all orders at this hour (9 PM broker time)
input int OrderPurgeMinute = 0; // Order purge minute (Broker Time)

//|------------------------ ADVANCED OPTIONS ----------------------|
input string SECTION_7 = "--- Advanced Features ---";
input group "=== ADVANCED FEATURES ==="
input ENUM_ENTRY_MODE EntryMode = ENTRY_WICK; // Default: Wick breakouts (Option A). Set ENTRY_BODY for Option B
input ENUM_SL_MODE StopLossMode = SL_RANGE_LOW; // SL: range low/high, mid-range, percent, points
input double SLPercent = 50.0; // For SL_PERCENT: % between wick high/low
input double SLPoints = 0; // For SL_POINTS: points from entry
input bool EnableBreakoutConfirmation = false; // Require Candle Close Confirmation

input bool EnableKillSwitch = false; // Manual kill-switch
input bool EnablePocketZones = false; // Optional: Draw/track pocket zones
input bool UseEMABias = false; // Use EMA Bias Filter
input int EMAPeriod = 50; // EMA Period
input double MinATRValue = 20; // Minimum ATR value in pips for volatility filter

//|------------------------ VISUAL SETTINGS ----------------------|
input string SECTION_8 = "--- Visuals & Dashboard ---";
input group "=== VISUAL & DASHBOARD ==="
input bool ShowDashboard = true; // Show Dashboard
input bool ShowTradeHistory = true; // Show Last 5 Trades
input bool ShowRiskMetrics = true; // Show Risk Metrics
input bool ShowLevels = true; // Show Price Levels
input bool DrawZones = true; // Draw Daily Zones
input int DashboardCorner = 0; // Dashboard Corner (0-3)
input int DashboardX = 20; // Dashboard X Position
input int DashboardY = 30; // Dashboard Y Position
input int FontSize = 10; // Dashboard Font Size
input string FontName = "Arial"; // Dashboard Font
input color HeaderColor = clrGold; // Header Color
input color ProfitColor = clrLimeGreen; // Profit Color
input color LossColor = clrRed; // Loss Color
input color BuyZoneColor = clrLime; // Buy Zone Color
input color SellZoneColor = clrRed; // Sell Zone Color
input color DashboardColor = clrWhite; // Dashboard Background Color



//--- Global Variables
double g_DailyHigh, g_DailyLow, g_DailyOpen, g_DailyClose;
// Only using wick high/low (range high/low) - no body levels needed
double g_BuyStopPrice, g_SellStopPrice;
double g_BuyStopLoss, g_SellStopLoss;
double g_BuyTakeProfit, g_SellTakeProfit;
bool g_NewDayProcessed = false;
bool g_BuyTradeToday = false;
bool g_SellTradeToday = false;

// Re-entry filters
bool buyTradeStopped = false;
bool sellTradeStopped = false;

// Dashboard Variables
double g_StartingBalance;
double g_DailyStartBalance;
double g_WeeklyStartBalance;
double g_MonthlyStartBalance;
double g_MaxDailyLoss = 0;
double g_MaxWeeklyLoss = 0;
double g_MaxMonthlyLoss = 0;
double g_MaxDailyLossPercent = 0;
double g_MaxWeeklyLossPercent = 0;
double g_MaxMonthlyLossPercent = 0;
int g_TotalTrades = 0;
int g_WinningTrades = 0;
int g_LosingTrades = 0;
int g_DailyTrades = 0;
int g_WeeklyTrades = 0;
int g_MonthlyTrades = 0;
double g_DailyPL = 0;
double g_WeeklyPL = 0;
double g_MonthlyPL = 0;
double g_TotalPL = 0;
datetime g_LastDayCheck = 0;
datetime g_LastWeekCheck = 0;
datetime g_LastMonthCheck = 0;
bool g_DailyLimitReached = false;
bool g_WeeklyLimitReached = false;
bool g_MonthlyLimitReached = false;

// Trade Management Variables
struct TradeInfo {
   int ticket;
   double openPrice;
   double initialSL;
   double initialTP;
   bool breakEvenSet;
   bool partialTP1Done;
   bool partialTP2Done;
   datetime openTime;
};
TradeInfo g_ActiveTrades[100]; // Support up to 100 concurrent trades
int g_ActiveTradeCount = 0;

//--- Input parameters are now properly organized above

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
   // Auto-generate MagicNumber if enabled
   if(AutoMagicNumber) {
      MagicNumber = GenerateMagicNumber();
      Print("Auto MagicNumber set to: ", MagicNumber);
   }

   // Initialize balance tracking
   g_StartingBalance = AccountBalance();
   g_DailyStartBalance = AccountBalance();
   g_WeeklyStartBalance = AccountBalance();
   g_MonthlyStartBalance = AccountBalance();

   // Initialize time tracking
   g_LastDayCheck = TimeCurrent();
   g_LastWeekCheck = TimeCurrent();
   g_LastMonthCheck = TimeCurrent();

   // Initialize counters
   g_TotalTrades = 0;
   g_WinningTrades = 0;
   g_LosingTrades = 0;
   g_DailyTrades = 0;
   g_WeeklyTrades = 0;
   g_MonthlyTrades = 0;
   g_ActiveTradeCount = 0;

   // Initialize P&L tracking
   g_DailyPL = 0;
   g_WeeklyPL = 0;
   g_MonthlyPL = 0;
   g_TotalPL = 0;

   // Initialize limit flags
   g_DailyLimitReached = false;
   g_WeeklyLimitReached = false;
   g_MonthlyLimitReached = false;

   Print("=== DAILY BREAKOUT MASTER BOT MT4 ===");
   Print("Developed by: Brian Alvin Bagorogoza");
   Print("InnovationX International");
   Print("Contact: +49 1521 6294394");
   Print("Advanced Risk Management & Dashboard Enabled");

   // Calculate levels immediately if EA starts mid-day
   if(g_BuyStopPrice == 0 && g_SellStopPrice == 0) {
      Print("Calculating daily levels from previous day's candle...");

      // Get previous day's OHLC data
      int shift = 1; // Previous day
      g_DailyHigh = iHigh(Symbol(), PERIOD_D1, shift);
      g_DailyLow = iLow(Symbol(), PERIOD_D1, shift);
      g_DailyOpen = iOpen(Symbol(), PERIOD_D1, shift);
      g_DailyClose = iClose(Symbol(), PERIOD_D1, shift);

      // Set breakout levels
      g_BuyStopPrice = g_DailyHigh;
      g_SellStopPrice = g_DailyLow;

      // Calculate stop losses and take profits
      CalculateStopLosses();
      CalculateTakeProfits();

      // Check current price position and set trade flags accordingly
      double currentPrice = Close[0];

      if(currentPrice > g_BuyStopPrice) {
         // Price already broke above range high - only allow SELL trades
         g_BuyTradeToday = true; // Block buy trades
         g_SellTradeToday = false; // Allow sell trades
         Print("EA started with price ABOVE range. Only SELL trades allowed at: ", g_SellStopPrice);
      }
      else if(currentPrice < g_SellStopPrice) {
         // Price already broke below range low - only allow BUY trades
         g_BuyTradeToday = false; // Allow buy trades
         g_SellTradeToday = true; // Block sell trades
         Print("EA started with price BELOW range. Only BUY trades allowed at: ", g_BuyStopPrice);
      }
      else {
         // Price inside range - allow both trades
         g_BuyTradeToday = false; // Allow buy trades
         g_SellTradeToday = false; // Allow sell trades
         Print("EA started with price INSIDE range. Both trades allowed - BUY: ", g_BuyStopPrice, " SELL: ", g_SellStopPrice);
      }

      // Draw zones if enabled
      if(DrawZones) DrawDailyZones();

      Print("Levels calculated - Ready to trade!");
   }

   Print("Bot Initialized Successfully!");
   return(INIT_SUCCEEDED);
}

int GenerateMagicNumber() {
   // Combines account, symbol, timeframe to a reproducible unique magic
   long acc = AccountNumber();
   string sym = Symbol();
   int tf = Period();
   // Simple hash-like combination (stay within 32-bit int)
   int h = (int)(acc % 1000000);
   for(int i=0;i<StringLen(sym);i++) h = (h*31 + StringGetChar(sym,i)) & 0x7FFFFFFF;
   h = (h*31 + tf) & 0x7FFFFFFF;
   // Ensure non-zero and in a friendly range
   if(h < 10000) h += 10000;
   return h;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
   // Clean up objects
   ObjectsDeleteAll(0, "DB_");
   Print("Daily Breakout Master Bot MT4 - InnovationX International - Deinitialized");
}



//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
   // Check for order purge at specified broker time
   CheckOrderPurge();

   // Check for new day at trigger time
   CheckNewDay();

   // Update dashboard
   if(ShowDashboard) UpdateDashboard();

   // Check for trade triggers (blocked during purge period)
   if(!IsOrderPurgePeriod()) {
      CheckTradeSignals();
   }

   // Manage existing trades
   ManageExistingTrades();

   // Update daily/weekly statistics
   UpdateStatistics();
}
//+------------------------------------------------------------------+
//| Check for order purge at specified broker time                  |
//+------------------------------------------------------------------+
void CheckOrderPurge() {
   static bool ordersPurgedToday = false;
   static int lastPurgeDay = -1;

   // Use broker time directly
   datetime currentTime = TimeCurrent();
   int currentHour = TimeHour(currentTime);
   int currentMinute = TimeMinute(currentTime);
   int currentDay = TimeDay(currentTime);

   // Reset purge flag on new day
   if(currentDay != lastPurgeDay) {
      ordersPurgedToday = false;
      lastPurgeDay = currentDay;
   }

   // Check if it's purge time (broker time)
   if(currentHour == OrderPurgeHour && currentMinute == OrderPurgeMinute && !ordersPurgedToday) {
      PurgeAllOrders();
      ordersPurgedToday = true;
      Print("Order purge executed at ", OrderPurgeHour, ":", OrderPurgeMinute, " broker time (", currentTime, ")");
   }
}

//+------------------------------------------------------------------+
//| Check if we're in order purge period                            |
//+------------------------------------------------------------------+
bool IsOrderPurgePeriod() {
   datetime currentTime = TimeCurrent();
   int currentHour = TimeHour(currentTime);
   // Between purge hour and midnight (00:00) broker time - no new trades allowed
   return (currentHour >= OrderPurgeHour && currentHour < 24);
}

//+------------------------------------------------------------------+
//| Purge all orders and close all positions (Gap Protection)       |
//+------------------------------------------------------------------+
void PurgeAllOrders() {
   int totalOrders = OrdersTotal();
   int deletedCount = 0;
   int closedCount = 0;

   for(int i = totalOrders - 1; i >= 0; i--) {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
         if(OrderMagicNumber() == MagicNumber && OrderSymbol() == Symbol()) {

            // Close open positions (market orders)
            if(OrderType() == OP_BUY || OrderType() == OP_SELL) {
               double closePrice = (OrderType() == OP_BUY) ? Bid : Ask;
               if(OrderClose(OrderTicket(), OrderLots(), closePrice, 3)) {
                  closedCount++;
                  Print("Closed position: ", OrderTicket(), " Type: ", OrderType() == OP_BUY ? "BUY" : "SELL");
               } else {
                  Print("Failed to close position: ", OrderTicket(), " Error: ", GetLastError());
               }
            }

            // Delete pending orders
            else if(OrderType() == OP_BUYSTOP || OrderType() == OP_SELLSTOP ||
                    OrderType() == OP_BUYLIMIT || OrderType() == OP_SELLLIMIT) {
               if(OrderDelete(OrderTicket())) {
                  deletedCount++;
                  Print("Deleted pending order: ", OrderTicket());
               } else {
                  Print("Failed to delete order: ", OrderTicket(), " Error: ", GetLastError());
               }
            }
         }
      }
   }

   Print("GAP PROTECTION: Purge complete. Closed ", closedCount, " positions, Deleted ", deletedCount, " pending orders");
}

//+------------------------------------------------------------------+
//| Check for new day and process daily levels                      |
//+------------------------------------------------------------------+
void CheckNewDay() {
   // Use broker time for daily reset
   datetime currentTime = TimeCurrent();
   int currentHour = TimeHour(currentTime);
   int currentMinute = TimeMinute(currentTime);

   // Check if it's trigger time and we haven't processed today (broker time)
   if(currentHour == TriggerHour && currentMinute == TriggerMinute) {
      if(!g_NewDayProcessed || TimeDay(currentTime) != TimeDay(g_LastDayCheck)) {
         ProcessNewDay();
         g_NewDayProcessed = true;
         g_LastDayCheck = currentTime;
         Print("New day processed at broker time: ", currentTime);
      }
   } else {
      g_NewDayProcessed = false;
   }
}


//+------------------------------------------------------------------+
//| Process new day - calculate levels and set pending orders       |
//+------------------------------------------------------------------+
void ProcessNewDay() {
   // Get IMMEDIATE previous day's OHLC (yesterday's completed candle)
   int shift = 1; // Previous day
   g_DailyHigh = iHigh(Symbol(), PERIOD_D1, shift);
   g_DailyLow = iLow(Symbol(), PERIOD_D1, shift);
   g_DailyOpen = iOpen(Symbol(), PERIOD_D1, shift);
   g_DailyClose = iClose(Symbol(), PERIOD_D1, shift);

   // SIMPLIFIED: Only use wick high/low (range high/low)
   // Remove all body logic - just use highest and lowest points of previous day
   g_BuyStopPrice = g_DailyHigh;   // BUY at range high breakout
   g_SellStopPrice = g_DailyLow;   // SELL at range low breakout

   // Stop loss logic
   if (StopLossMode == SL_RANGE_LOW) {
      // BUY SL = range low, SELL SL = range high
      g_BuyStopLoss = g_DailyLow;
      g_SellStopLoss = g_DailyHigh;
   } else if (StopLossMode == SL_MID_RANGE) {
      // SL at middle of range for both
      double mid = (g_DailyHigh + g_DailyLow) / 2.0;
      g_BuyStopLoss = mid;
      g_SellStopLoss = mid;
   } else if (StopLossMode == SL_PERCENT) {
      double range = g_DailyHigh - g_DailyLow;
      g_BuyStopLoss = g_DailyLow + (range * (SLPercent / 100.0));
      g_SellStopLoss = g_DailyHigh - (range * (SLPercent / 100.0));
   } else if (StopLossMode == SL_POINTS && SLPoints > 0) {
      g_BuyStopLoss = g_BuyStopPrice - (SLPoints * Point);
      g_SellStopLoss = g_SellStopPrice + (SLPoints * Point);
   }
   // Calculate take profits
   CalculateTakeProfits();

   // Reset daily trade flags
   g_BuyTradeToday = false;
   g_SellTradeToday = false;
   g_DailyTrades = 0;
   g_DailyStartBalance = AccountBalance();
   g_DailyPL = 0;

   // Draw zones if enabled
   if(DrawZones) DrawDailyZones();

   Print("New day processed. Buy Stop: ", g_BuyStopPrice, " Sell Stop: ", g_SellStopPrice);
   Print("DEBUG: Buy SL: ", g_BuyStopLoss, " Buy TP: ", g_BuyTakeProfit);
   Print("DEBUG: Sell SL: ", g_SellStopLoss, " Sell TP: ", g_SellTakeProfit);
}

//+------------------------------------------------------------------+
//| Calculate Stop Loss levels based on settings                    |
//+------------------------------------------------------------------+
void CalculateStopLosses() {
   switch(StopLossMode) {
      case SL_RANGE_LOW:
         // Default: SL at opposite side of range
         g_BuyStopLoss = g_DailyLow;   // Buy SL at range low
         g_SellStopLoss = g_DailyHigh; // Sell SL at range high
         break;

      case SL_MID_RANGE: {
         // SL at mid-range
         double midRange = (g_DailyHigh + g_DailyLow) / 2.0;
         g_BuyStopLoss = midRange;
         g_SellStopLoss = midRange;
         break;
      }

      case SL_POINTS:
         // SL at fixed points from entry
         g_BuyStopLoss = g_BuyStopPrice - (SLPoints * Point);
         g_SellStopLoss = g_SellStopPrice + (SLPoints * Point);
         break;
   }
}

//+------------------------------------------------------------------+
//| Calculate Take Profit levels based on settings                  |
//+------------------------------------------------------------------+
void CalculateTakeProfits() {
   double buyRisk = (g_BuyStopPrice - g_BuyStopLoss) / Point;
   double sellRisk = (g_SellStopLoss - g_SellStopPrice) / Point;

   switch(TakeProfitType) {
      case TP_FIXED_RR:
         g_BuyTakeProfit = g_BuyStopPrice + (buyRisk * FixedRiskReward * Point);
         g_SellTakeProfit = g_SellStopPrice - (sellRisk * FixedRiskReward * Point);
         break;

      case TP_ATR_BASED: {
         double atr = iATR(Symbol(), PERIOD_D1, ATRPeriod, 1);
         g_BuyTakeProfit = g_BuyStopPrice + (atr * ATRMultiplier);
         g_SellTakeProfit = g_SellStopPrice - (atr * ATRMultiplier);
         break;
      }
      case TP_CUSTOM_POINTS:
         // Direct points input (default 1000 points for indices/gold/BTC)
         g_BuyTakeProfit = g_BuyStopPrice + (CustomTPPoints * Point);
         g_SellTakeProfit = g_SellStopPrice - (CustomTPPoints * Point);
         break;
   }
}

//+------------------------------------------------------------------+
//| Check for trade signals and execute trades                      |
//+------------------------------------------------------------------+
void CheckTradeSignals() {
   // Debug: Check trading conditions
   bool tradingAllowed = IsTradingAllowed();
   bool volatilitySufficient = IsVolatilitySufficient();
   bool liquidityNormal = IsLiquidityNormal();

   if (!tradingAllowed) {
      static datetime lastDebugTime = 0;
      if(TimeCurrent() - lastDebugTime > 3600) { // Print once per hour
         Print("DEBUG: Trading not allowed. Check filters.");
         lastDebugTime = TimeCurrent();
      }
      return;
   }

   if (!volatilitySufficient || !liquidityNormal) return;

   // Use High/Low for breakout detection in backtesting
   double currentHigh = High[0];
   double currentLow = Low[0];
   double currentClose = Close[0];
   double spread = (Ask - Bid) / Point;

   // Check spread filter - but be more lenient in backtesting
   if(spread > MaxSpread && !IsTesting()) {
      static datetime lastSpreadDebug = 0;
      if(TimeCurrent() - lastSpreadDebug > 3600) {
         Print("DEBUG: Spread too high: ", spread, " > ", MaxSpread);
         lastSpreadDebug = TimeCurrent();
      }
      return;
   }

   // Check gap filter
   if(EnableGapFilter && CheckForGap()) {
      Print("DEBUG: Gap detected, skipping trades");
      return;
   }

   // Debug: Print current conditions
   static datetime lastConditionDebug = 0;
   if(TimeCurrent() - lastConditionDebug > 1800) { // Every 30 minutes
      Print("DEBUG: High=", currentHigh, " Low=", currentLow, " Close=", currentClose);
      Print("DEBUG: BuyStop=", g_BuyStopPrice, " SellStop=", g_SellStopPrice);
      Print("DEBUG: BuyTradeToday=", g_BuyTradeToday, " SellTradeToday=", g_SellTradeToday, " DailyTrades=", g_DailyTrades);
      lastConditionDebug = TimeCurrent();
   }

   // SMART ORDER PLACEMENT: Only place orders for levels not yet broken
   bool rangeHighBroken = (currentHigh >= g_BuyStopPrice);
   bool rangeLowBroken = (currentLow <= g_SellStopPrice);

   // Check buy signal - only if range high not yet broken
   if(!g_BuyTradeToday && g_DailyTrades < MaxDailyTrades && g_BuyStopPrice > 0) {
      if(rangeHighBroken) {
         // Range high broken - execute BUY immediately
         if(!EnableBreakoutConfirmation || currentClose > g_BuyStopPrice) {
            Print("DEBUG: Buy signal triggered! High=", currentHigh, " >= RangeHigh=", g_BuyStopPrice);
            ExecuteBuyTrade();
         }
      }
   }

   // Check sell signal - only if range low not yet broken
   if(!g_SellTradeToday && g_DailyTrades < MaxDailyTrades && g_SellStopPrice > 0) {
      if(rangeLowBroken) {
         // Range low broken - execute SELL immediately
         if(!EnableBreakoutConfirmation || currentClose < g_SellStopPrice) {
            Print("DEBUG: Sell signal triggered! Low=", currentLow, " <= RangeLow=", g_SellStopPrice);
            ExecuteSellTrade();
         } else {
            Print("DEBUG: Sell breakout needs confirmation. Close=", currentClose, " SellStop=", g_SellStopPrice);
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Execute Buy Trade                                                |
//+------------------------------------------------------------------+
void ExecuteBuyTrade() {
    if (buyTradeStopped) {
       Print("DEBUG: Buy trade blocked - buyTradeStopped flag is true");
       return;
    }

   double riskPips = g_BuyStopPrice - g_BuyStopLoss;
   double lotSize = CalculateLotSize(riskPips);

   Print("DEBUG: Buy trade attempt - Risk pips: ", riskPips/Point, " Lot size: ", lotSize);
   Print("DEBUG: Buy levels - Entry: ", g_BuyStopPrice, " SL: ", g_BuyStopLoss, " TP: ", g_BuyTakeProfit);

   if(lotSize > 0) {
      // Validate SL and TP levels
      double minStopLevel = MarketInfo(Symbol(), MODE_STOPLEVEL) * Point;
      bool validSL = (g_BuyStopLoss > 0 && (Ask - g_BuyStopLoss) >= minStopLevel);
      bool validTP = (g_BuyTakeProfit > 0 && (g_BuyTakeProfit - Ask) >= minStopLevel);

      Print("DEBUG: MinStopLevel: ", minStopLevel/Point, " pips");
      Print("DEBUG: SL Distance: ", (Ask - g_BuyStopLoss)/Point, " pips, Valid: ", validSL);
      Print("DEBUG: TP Distance: ", (g_BuyTakeProfit - Ask)/Point, " pips, Valid: ", validTP);

      // Use 0 for invalid SL/TP to let broker handle it
      double useSL = validSL ? g_BuyStopLoss : 0;
      double useTP = validTP ? g_BuyTakeProfit : 0;

      int ticket = OrderSend(Symbol(), OP_BUY, lotSize, Ask, Slippage,
                            useSL, useTP,
                            TradeComment + " Buy", MagicNumber, 0, clrGreen);

      if(ticket > 0) {
         g_BuyTradeToday = true;
         g_DailyTrades++;
         g_TotalTrades++;
         Print("SUCCESS: Buy trade executed. Ticket: ", ticket, " Lot size: ", lotSize);
      } else {
         int error = GetLastError();
         Print("ERROR: Buy trade failed. Error: ", error, " - ", ErrorDescription(error));
         Print("DEBUG: Ask: ", Ask, " SL: ", useSL, " TP: ", useTP);
      }
   } else {
      Print("ERROR: Buy trade failed - Invalid lot size: ", lotSize);
   }
}

//+------------------------------------------------------------------+
//| Execute Sell Trade                                               |
//+------------------------------------------------------------------+
void ExecuteSellTrade() {
    if (sellTradeStopped) {
       Print("DEBUG: Sell trade blocked - sellTradeStopped flag is true");
       return;
    }

   double riskPips = g_SellStopLoss - g_SellStopPrice;
   double lotSize = CalculateLotSize(riskPips);

   Print("DEBUG: Sell trade attempt - Risk pips: ", riskPips/Point, " Lot size: ", lotSize);
   Print("DEBUG: Sell levels - Entry: ", g_SellStopPrice, " SL: ", g_SellStopLoss, " TP: ", g_SellTakeProfit);

   if(lotSize > 0) {
      // Validate SL and TP levels
      double minStopLevel = MarketInfo(Symbol(), MODE_STOPLEVEL) * Point;
      bool validSL = (g_SellStopLoss > 0 && (g_SellStopLoss - Bid) >= minStopLevel);
      bool validTP = (g_SellTakeProfit > 0 && (Bid - g_SellTakeProfit) >= minStopLevel);

      Print("DEBUG: MinStopLevel: ", minStopLevel/Point, " pips");
      Print("DEBUG: SL Distance: ", (g_SellStopLoss - Bid)/Point, " pips, Valid: ", validSL);
      Print("DEBUG: TP Distance: ", (Bid - g_SellTakeProfit)/Point, " pips, Valid: ", validTP);

      // Use 0 for invalid SL/TP to let broker handle it
      double useSL = validSL ? g_SellStopLoss : 0;
      double useTP = validTP ? g_SellTakeProfit : 0;

      int ticket = OrderSend(Symbol(), OP_SELL, lotSize, Bid, Slippage,
                            useSL, useTP,
                            TradeComment + " Sell", MagicNumber, 0, clrRed);

      if(ticket > 0) {
         g_SellTradeToday = true;
         g_DailyTrades++;
         g_TotalTrades++;
         Print("SUCCESS: Sell trade executed. Ticket: ", ticket, " Lot size: ", lotSize);
      } else {
         int error = GetLastError();
         Print("ERROR: Sell trade failed. Error: ", error, " - ", ErrorDescription(error));
         Print("DEBUG: Bid: ", Bid, " SL: ", useSL, " TP: ", useTP);
      }
   } else {
      Print("ERROR: Sell trade failed - Invalid lot size: ", lotSize);
   }
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk management                     |
//+------------------------------------------------------------------+
double CalculateLotSize(double riskPips) {
   double lotSize = 0;
   double riskPipsInPoints = riskPips / Point;

   Print("DEBUG: CalculateLotSize - Risk pips: ", riskPipsInPoints, " points");

   switch(LotSizeType) {
      case LOT_MANUAL:
         lotSize = ManualLotSize;
         Print("DEBUG: Using manual lot size: ", lotSize);
         break;

      case LOT_RISK_BASED: {
         double balance = AccountBalance();
         double riskAmount = balance * RiskPercentPerTrade / 100.0;
         double pipValue = MarketInfo(Symbol(), MODE_TICKVALUE);
         if(Point == 0.00001 || Point == 0.001) pipValue *= 10;

         Print("DEBUG: Balance: ", balance, " Risk%: ", RiskPercentPerTrade, " RiskAmount: ", riskAmount);
         Print("DEBUG: PipValue: ", pipValue, " RiskPips: ", riskPipsInPoints);

         if(riskPipsInPoints > 0 && pipValue > 0) {
            lotSize = riskAmount / (riskPipsInPoints * pipValue);
            Print("DEBUG: Calculated lot size before normalization: ", lotSize);
         } else {
            Print("ERROR: Invalid risk pips or pip value");
            return 0;
         }
         break;
      }
   }

   // Normalize lot size
   double minLot = MarketInfo(Symbol(), MODE_MINLOT);
   double maxLot = MarketInfo(Symbol(), MODE_MAXLOT);
   double lotStep = MarketInfo(Symbol(), MODE_LOTSTEP);

   Print("DEBUG: Lot constraints - Min: ", minLot, " Max: ", maxLot, " Step: ", lotStep);

   if(lotStep > 0) {
      lotSize = MathMax(minLot, MathMin(maxLot, NormalizeDouble(lotSize / lotStep, 0) * lotStep));
   } else {
      lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
   }

   Print("DEBUG: Final normalized lot size: ", lotSize);
   return lotSize;
}

//+------------------------------------------------------------------+
//| Manage existing trades (trailing stop, partial TP, etc.)       |
//+------------------------------------------------------------------+
void ManageExistingTrades() {
   for(int i = OrdersTotal() - 1; i >= 0; i--) {
      if(OrderSelect(i, SELECT_BY_POS) && OrderMagicNumber() == MagicNumber) {
         // Apply Break-Even first (sl to entry when in profit)
         if(EnableBreakEven) {
            ApplyBreakEven();
         }

         // Trailing stop (follows price)
         if(EnableTrailingStop) {
            ApplyTrailingStop();
         }

         // Optional partial take profit
         if(EnablePartialTP) {
            CheckPartialTakeProfit();
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Apply Break-Even: move SL to entry when profit >= BreakEvenPoints|
//+------------------------------------------------------------------+
void ApplyBreakEven() {
   double entry = OrderOpenPrice();
   double pointDist = BreakEvenPoints * Point;

   if(OrderType() == OP_BUY) {
      if(Bid - entry >= pointDist) {
         double newSL = entry; // exact breakeven
         if(newSL > OrderStopLoss() && newSL < Bid) {
            if(!OrderModify(OrderTicket(), OrderOpenPrice(), newSL, OrderTakeProfit(), 0, clrBlue)) {
               int err = GetLastError();
               Print("WARN: BreakEven modify failed (BUY). Error ", err);
            }
         }
      }
   } else if(OrderType() == OP_SELL) {
      if(entry - Ask >= pointDist) {
         double newSL = entry; // exact breakeven
         if(newSL < OrderStopLoss() && newSL > Ask) {
            if(!OrderModify(OrderTicket(), OrderOpenPrice(), newSL, OrderTakeProfit(), 0, clrBlue)) {
               int err = GetLastError();
               Print("WARN: BreakEven modify failed (SELL). Error ", err);
            }
         }
      }
   }
}


//+------------------------------------------------------------------+
//| Apply trailing stop to profitable trades                        |
//+------------------------------------------------------------------+
void ApplyTrailingStop() {
   double trailDistance = TrailingStopPoints * Point;

   if(OrderType() == OP_BUY) {
      double newSL = Bid - trailDistance;
      if(newSL > OrderStopLoss() && newSL < Bid) {
         bool mod = OrderModify(OrderTicket(), OrderOpenPrice(), newSL, OrderTakeProfit(), 0, clrBlue);
      }
   } else if(OrderType() == OP_SELL) {
      double newSL = Ask + trailDistance;
      if(newSL < OrderStopLoss() && newSL > Ask) {
         bool mod = OrderModify(OrderTicket(), OrderOpenPrice(), newSL, OrderTakeProfit(), 0, clrBlue);
      }
   }
}

//+------------------------------------------------------------------+
//| Check and execute partial take profit                           |
//+------------------------------------------------------------------+
void CheckPartialTakeProfit() {
    if(!EnablePartialTP) return;

    double profit = OrderProfit();
    double risk = MathAbs(OrderOpenPrice() - OrderStopLoss()) * OrderLots() * MarketInfo(Symbol(), MODE_TICKVALUE);

    if(profit >= risk) { // At 1:1 RR
        double partialLots = OrderLots() * PartialTPPercent / 100.0;
        double remainingLots = OrderLots() - partialLots;

        // Close partial position
        if(OrderClose(OrderTicket(), partialLots,
           OrderType() == OP_BUY ? Bid : Ask, Slippage, clrBlue)) {

            // Open new position with remaining lots and original SL/TP
            if(OrderType() == OP_BUY) {
                int ticket = OrderSend(Symbol(), OP_BUY, remainingLots, Ask, Slippage,
                         OrderStopLoss(), OrderTakeProfit(),
                         TradeComment + " Partial", MagicNumber, 0, clrGreen);
            } else {
                int ticket = OrderSend(Symbol(), OP_SELL, remainingLots, Bid, Slippage,
                         OrderStopLoss(), OrderTakeProfit(),
                         TradeComment + " Partial", MagicNumber, 0, clrRed);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Check if trading is allowed based on filters                    |
//+------------------------------------------------------------------+
bool IsTradingAllowed() {
   if(EnableKillSwitch) {
      Print("DEBUG: Trading blocked - Kill switch enabled");
      return false;
   }

   // Check daily loss limit
   if(g_DailyLimitReached) {
      Print("DEBUG: Trading blocked - Daily loss limit reached");
      return false;
   }

   // Check weekly loss limit
   if(g_WeeklyLimitReached) {
      Print("DEBUG: Trading blocked - Weekly loss limit reached");
      return false;
   }

   // Check monthly loss limit
   if(g_MonthlyLimitReached) {
      Print("DEBUG: Trading blocked - Monthly loss limit reached");
      return false;
   }

   // Check equity guard
   if(EnableEquityGuard) {
      double currentEquity = AccountEquity();
      double equityThreshold = g_StartingBalance * (100 - EquityGuardPercent) / 100.0;
      if(currentEquity < equityThreshold) {
         static bool equityGuardTriggered = false;
         if(!equityGuardTriggered) {
            equityGuardTriggered = true;
            Print("ALERT: Equity guard triggered! Equity: ", currentEquity, " < Threshold: ", equityThreshold);
            Print("EMERGENCY: Closing all trades and deleting all orders!");
            PurgeAllOrders(); // Close all positions and delete all orders
         }
         return false;
      }
   }

   // Check max drawdown
   double drawdown = (g_StartingBalance - AccountEquity()) / g_StartingBalance * 100.0;
   if(drawdown > MaxDrawdownPercent) {
      static bool maxDrawdownTriggered = false;
      if(!maxDrawdownTriggered) {
         maxDrawdownTriggered = true;
         Print("ALERT: Max drawdown exceeded! Drawdown: ", drawdown, "% > ", MaxDrawdownPercent, "%");
         Print("EMERGENCY: Closing all trades and deleting all orders!");
         PurgeAllOrders(); // Close all positions and delete all orders
      }
      return false;
   }

   // Check daily loss limit
   double dailyLoss = g_DailyStartBalance - AccountEquity();
   double dailyLossPercent = (dailyLoss / g_DailyStartBalance) * 100.0;
   if(dailyLossPercent > MaxDailyLossPercent) {
      if(!g_DailyLimitReached) {
         g_DailyLimitReached = true;
         Print("ALERT: Daily loss limit reached! Loss: ", dailyLossPercent, "% > ", MaxDailyLossPercent, "%");
         Print("EMERGENCY: Closing all trades and deleting all orders!");
         PurgeAllOrders(); // Close all positions and delete all orders
      }
      return false;
   }

   // Check weekly loss limit
   double weeklyLoss = g_WeeklyStartBalance - AccountEquity();
   double weeklyLossPercent = (weeklyLoss / g_WeeklyStartBalance) * 100.0;
   if(weeklyLossPercent > MaxWeeklyLossPercent) {
      if(!g_WeeklyLimitReached) {
         g_WeeklyLimitReached = true;
         Print("ALERT: Weekly loss limit reached! Loss: ", weeklyLossPercent, "% > ", MaxWeeklyLossPercent, "%");
         Print("EMERGENCY: Closing all trades and deleting all orders!");
         PurgeAllOrders(); // Close all positions and delete all orders
      }
      return false;
   }

   // Check monthly loss limit
   double monthlyLoss = g_MonthlyStartBalance - AccountEquity();
   double monthlyLossPercent = (monthlyLoss / g_MonthlyStartBalance) * 100.0;
   if(monthlyLossPercent > MaxMonthlyLossPercent) {
      g_MonthlyLimitReached = true;
      Print("ALERT: Monthly loss limit reached! Loss: ", monthlyLossPercent, "% > ", MaxMonthlyLossPercent, "%");
      return false;
   }

   // Check day of week
   int dayOfWeek = TimeDayOfWeek(TimeCurrent());
   switch(dayOfWeek) {
      case 1: if(!EnableMondayTrading) { Print("DEBUG: Trading blocked - Monday trading disabled"); return false; } break;
      case 2: if(!EnableTuesdayTrading) { Print("DEBUG: Trading blocked - Tuesday trading disabled"); return false; } break;
      case 3: if(!EnableWednesdayTrading) { Print("DEBUG: Trading blocked - Wednesday trading disabled"); return false; } break;
      case 4: if(!EnableThursdayTrading) { Print("DEBUG: Trading blocked - Thursday trading disabled"); return false; } break;
      case 5: if(!EnableFridayTrading) { Print("DEBUG: Trading blocked - Friday trading disabled"); return false; } break;
   }

   // Check weekly risk limit
   double weeklyRisk = MathAbs(g_WeeklyPL) / g_WeeklyStartBalance * 100.0;
   if(weeklyRisk > MaxRiskPerWeek) {
      Print("DEBUG: Trading blocked - Weekly risk limit. Risk: ", weeklyRisk, "% > ", MaxRiskPerWeek, "%");
      return false;
   }

   // News filter logic
   if(EnableNewsFilter && IsNewsEvent(TimeCurrent())) {
      Print("DEBUG: Trading blocked - News event detected");
      return false;
   }

   // Check session hours (skip in backtesting)
   if (!IsTesting() && !IsWithinSession()) {
      static datetime lastSessionDebug = 0;
      if(TimeCurrent() - lastSessionDebug > 3600) {
         int currentHour = TimeHour(TimeCurrent());
         Print("DEBUG: Trading blocked - Outside session hours. Current: ", currentHour, " Session: ", SessionStartHour, "-", SessionEndHour);
         lastSessionDebug = TimeCurrent();
      }
      return false;
   }

   return true;
}

bool IsWithinSession() {
    int hour = TimeHour(TimeCurrent());
    return (hour >= SessionStartHour && hour < SessionEndHour);
}

//+------------------------------------------------------------------+
//| Check for gap conditions and apply protection strategy           |
//+------------------------------------------------------------------+
bool CheckForGap() {
   double currentOpen = Open[0];
   double gapUp = (currentOpen - g_DailyHigh) / Point;   // Gap above yesterday's high
   double gapDown = (g_DailyLow - currentOpen) / Point;  // Gap below yesterday's low

   bool hasGapUp = (gapUp > MinGapSize);
   bool hasGapDown = (gapDown > MinGapSize);

   if(!hasGapUp && !hasGapDown) {
      return false; // No significant gap
   }

   // Gap detected - apply protection strategy
   if(hasGapUp) {
      Print("GAP UP detected! Open: ", currentOpen, " > Yesterday High: ", g_DailyHigh, " Gap: ", gapUp, " points");

      switch(GapMode) {
         case GAP_SKIP:
            Print("GAP PROTECTION: Skipping all trades today (GAP_SKIP mode)");
            return true; // Skip all trades

         case GAP_REVERSE:
            Print("GAP PROTECTION: Gap up - blocking BUY trades, allowing SELL reversals only");
            g_BuyTradeToday = true;   // Block buy trades (already "triggered" by gap)
            g_SellTradeToday = false; // Allow sell trades back to range
            return false; // Continue trading with restrictions

         case GAP_ADJUST:
            Print("GAP PROTECTION: Adjusting BUY level to gap open: ", currentOpen);
            g_BuyStopPrice = currentOpen; // Use gap open as new breakout level
            CalculateStopLosses(); // Recalculate SL/TP
            CalculateTakeProfits();
            return false; // Continue trading with adjusted levels
      }
   }

   if(hasGapDown) {
      Print("GAP DOWN detected! Open: ", currentOpen, " < Yesterday Low: ", g_DailyLow, " Gap: ", gapDown, " points");

      switch(GapMode) {
         case GAP_SKIP:
            Print("GAP PROTECTION: Skipping all trades today (GAP_SKIP mode)");
            return true; // Skip all trades

         case GAP_REVERSE:
            Print("GAP PROTECTION: Gap down - blocking SELL trades, allowing BUY reversals only");
            g_BuyTradeToday = false;  // Allow buy trades back to range
            g_SellTradeToday = true;  // Block sell trades (already "triggered" by gap)
            return false; // Continue trading with restrictions

         case GAP_ADJUST:
            Print("GAP PROTECTION: Adjusting SELL level to gap open: ", currentOpen);
            g_SellStopPrice = currentOpen; // Use gap open as new breakout level
            CalculateStopLosses(); // Recalculate SL/TP
            CalculateTakeProfits();
            return false; // Continue trading with adjusted levels
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Draw daily zones on chart                                        |
//+------------------------------------------------------------------+
void DrawDailyZones() {
   // Delete existing zones
   ObjectDelete("DB_BuyZone");
   ObjectDelete("DB_SellZone");
   ObjectDelete("DB_WickRange");
   ObjectDelete("DB_BodyRange");
   ObjectDelete("DB_WickHighLine");
   ObjectDelete("DB_WickLowLine");
   ObjectDelete("DB_Label_WickHigh");
   ObjectDelete("DB_Label_WickLow");
   ObjectDelete("DB_Label_BodyHigh");
   ObjectDelete("DB_Label_BodyLow");

   // Compute full next-day window so levels persist all next day
   datetime currentTime   = TimeCurrent();
   datetime currentDayStart = (datetime)(MathFloor((double)currentTime / 86400.0) * 86400.0);
   datetime nextDayStart  = currentDayStart + 86400;         // start of next day
   datetime nextDayEnd    = nextDayStart + 86400;             // end of next day

   // Draw clean wick range rectangle with minimal styling
   ObjectCreate("DB_WickRange", OBJ_RECTANGLE, 0, nextDayStart, g_DailyHigh, nextDayEnd, g_DailyLow);
   ObjectSet("DB_WickRange", OBJPROP_COLOR, clrLightGray);
   ObjectSet("DB_WickRange", OBJPROP_STYLE, STYLE_SOLID);
   ObjectSet("DB_WickRange", OBJPROP_WIDTH, 1);
   ObjectSet("DB_WickRange", OBJPROP_BACK, true);
   ObjectSet("DB_WickRange", OBJPROP_FILL, false); // Clean outline only

   // SIMPLIFIED: Only draw wick range (no body range needed)
   // Body range removed - using only wick high/low breakouts

   // Draw clean breakout levels with professional styling
   ObjectCreate("DB_WickHighLine", OBJ_HLINE, 0, 0, g_DailyHigh);
   ObjectSet("DB_WickHighLine", OBJPROP_COLOR, BuyZoneColor);
   ObjectSet("DB_WickHighLine", OBJPROP_STYLE, STYLE_SOLID);
   ObjectSet("DB_WickHighLine", OBJPROP_WIDTH, 2);

   ObjectCreate("DB_WickLowLine", OBJ_HLINE, 0, 0, g_DailyLow);
   ObjectSet("DB_WickLowLine", OBJPROP_COLOR, SellZoneColor);
   ObjectSet("DB_WickLowLine", OBJPROP_STYLE, STYLE_SOLID);
   ObjectSet("DB_WickLowLine", OBJPROP_WIDTH, 2);

   // SIMPLIFIED: Only wick high/low lines (range high/low)
   // Body high/low lines removed - using only wick breakouts

   // Clean minimal labels for level identification
   if(ShowLevels) {
      string sRangeHigh = "BUY: " + DoubleToStr(g_DailyHigh, Digits);
      string sRangeLow  = "SELL: " + DoubleToStr(g_DailyLow,  Digits);

      // Place labels with clean positioning
      datetime labelTime = nextDayStart + 3600; // 1 hour into next day

      ObjectCreate("DB_Label_RangeHigh", OBJ_TEXT, 0, labelTime, g_DailyHigh + 5*Point);
      ObjectSetText("DB_Label_RangeHigh", sRangeHigh, 9, "Arial Bold", BuyZoneColor);

      ObjectCreate("DB_Label_RangeLow", OBJ_TEXT, 0, labelTime, g_DailyLow - 15*Point);
      ObjectSetText("DB_Label_RangeLow", sRangeLow, 9, "Arial Bold", SellZoneColor);
   }
}

//+------------------------------------------------------------------+
//| Draw pocket zones on chart (optional feature)                   |
//+------------------------------------------------------------------+
void DrawPocketZones() {
    if(!EnablePocketZones) return;

    static double lastHigh = 0;
    static double lastLow = EMPTY_VALUE;

    // SIMPLIFIED: Pocket zones removed (body levels not used)
    // Only using range high/low breakouts now
}

//+------------------------------------------------------------------+
//| Fixed CreateLabel function                                      |
//+------------------------------------------------------------------+
void CreateLabel(string name, string text, int x, int y, color clr,
                int size = 10, string font = "Arial", int corner = 0) {
    if(ObjectFind(name) == -1)
        ObjectCreate(name, OBJ_LABEL, 0, 0, 0);

    ObjectSetText(name, text, size, font, clr);
    ObjectSet(name, OBJPROP_CORNER, corner);
    ObjectSet(name, OBJPROP_XDISTANCE, x);
    ObjectSet(name, OBJPROP_YDISTANCE, y);
}

//+------------------------------------------------------------------+
//| Update and display beautiful minimal dashboard                   |
//+------------------------------------------------------------------+
void UpdateDashboard() {
   // Clean minimal design inspired by professional indicators
   int yPos = DashboardY;
   int xPos = DashboardX;
   int lineHeight = FontSize + 6; // Better spacing like the indicator

   // Header with your branding - cleaner typography
   CreateLabel("DB_Header", "DAILY BREAKOUT MASTER - InnovationX International",
               xPos, yPos, HeaderColor, FontSize + 1, FontName, DashboardCorner);
   yPos += lineHeight;

   CreateLabel("DB_Developer", "By Brian Alvin Bagorogoza | +49 1521 6294394",
               xPos, yPos, clrSilver, FontSize - 1, FontName, DashboardCorner);
   yPos += lineHeight + 8; // Extra spacing like the indicator

   // Account section with clean formatting
   CreateLabel("DB_Balance", "Account Balance: $" + DoubleToString(AccountBalance(), 2),
               xPos, yPos, DashboardColor, FontSize, FontName, DashboardCorner);
   yPos += lineHeight;

   // Daily P&L with color coding
   color dailyColor = (g_DailyPL >= 0) ? ProfitColor : LossColor;
   CreateLabel("DB_DailyPL", "Daily P&L: $" + DoubleToString(g_DailyPL, 2) +
               " (" + DoubleToString(g_DailyPL/g_DailyStartBalance*100, 2) + "%)",
               xPos, yPos, dailyColor, FontSize, FontName, DashboardCorner);
   yPos += lineHeight;

   // Risk metrics
   double riskAmount = AccountBalance() * RiskPercentPerTrade / 100.0;
   CreateLabel("DB_RiskPerTrade", "Risk per Trade: $" + DoubleToString(riskAmount, 2) +
               " (" + DoubleToString(RiskPercentPerTrade, 1) + "%)",
               xPos, yPos, clrGold, FontSize, FontName, DashboardCorner);
   yPos += lineHeight + 5; // Section spacing

   // Trading levels section
   CreateLabel("DB_BuyLevel", "Buy Stop: " + DoubleToString(g_BuyStopPrice, Digits),
               xPos, yPos, BuyZoneColor, FontSize, FontName, DashboardCorner);
   yPos += lineHeight;

   CreateLabel("DB_SellLevel", "Sell Stop: " + DoubleToString(g_SellStopPrice, Digits),
               xPos, yPos, SellZoneColor, FontSize, FontName, DashboardCorner);
   yPos += lineHeight + 5;

   // Status section
   string tradeStatus = "Today's Trades: " + IntegerToString(g_DailyTrades) + "/" + IntegerToString(MaxDailyTrades);
   color statusColor = (g_DailyTrades >= MaxDailyTrades) ? LossColor : DashboardColor;
   CreateLabel("DB_DailyTrades", tradeStatus, xPos, yPos, statusColor, FontSize, FontName, DashboardCorner);
}



//+------------------------------------------------------------------+
//| UpdateStatistics stub                                            |
//+------------------------------------------------------------------+
void UpdateStatistics() {
   // Implement statistics update logic here if needed.
}

//+------------------------------------------------------------------+
//| IsVolatilitySufficient stub                                      |
//+------------------------------------------------------------------+
bool IsVolatilitySufficient() {
   // Implement volatility filter logic here if needed.
   return true;
}

//+------------------------------------------------------------------+
//| IsLiquidityNormal stub                                           |
//+------------------------------------------------------------------+
bool IsLiquidityNormal() {
   // Implement liquidity filter logic here if needed.
   return true;
}

//+------------------------------------------------------------------+
//| Error Description Function                                       |
//+------------------------------------------------------------------+
string ErrorDescription(int error) {
   switch(error) {
      case 0: return "No error";
      case 1: return "No error returned";
      case 2: return "Common error";
      case 3: return "Invalid trade parameters";
      case 4: return "Trade server is busy";
      case 5: return "Old version of the client terminal";
      case 6: return "No connection with trade server";
      case 7: return "Not enough rights";
      case 8: return "Too frequent requests";
      case 9: return "Malfunctional trade operation";
      case 64: return "Account disabled";
      case 65: return "Invalid account";
      case 128: return "Trade timeout";
      case 129: return "Invalid price";
      case 130: return "Invalid stops";
      case 131: return "Invalid trade volume";
      case 132: return "Market is closed";
      case 133: return "Trade is disabled";
      case 134: return "Not enough money";
      case 135: return "Price changed";
      case 136: return "Off quotes";
      case 137: return "Broker is busy";
      case 138: return "Requote";
      case 139: return "Order is locked";
      case 140: return "Long positions only allowed";
      case 141: return "Too many requests";
      case 145: return "Modification denied because order too close to market";
      case 146: return "Trade context is busy";
      case 147: return "Expirations are denied by broker";
      case 148: return "Amount of open and pending orders has reached the limit";
      default: return "Unknown error " + IntegerToString(error);
   }
}

//+------------------------------------------------------------------+
//| News Filter Implementation                                       |
//+------------------------------------------------------------------+
bool IsNewsEvent(datetime time) {
    if(!EnableNewsFilter) return false;
    datetime currentTime = TimeCurrent();

    // Simple time-based news filter
    datetime startOfDay = (datetime)(MathFloor((double)currentTime / 86400.0) * 86400.0); // Start of current day
    datetime newsHours[] = {8, 12, 14, 20}; // Common news hours (GMT)

    for(int i = 0; i < ArraySize(newsHours); i++) {
        datetime newsTime = startOfDay + newsHours[i] * 3600;
        if(MathAbs(currentTime - newsTime) < NewsFilterMinutes * 60) {
            return true; // Within news window
        }
    }
    return false;
}
