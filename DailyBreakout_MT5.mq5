//+------------------------------------------------------------------+
//|                                           DailyBreakout_MT5.mq5 |
//|                                    Daily Breakout Master Bot MT5 |
//|                          BY BRIAN ALVIN BAGOROGOZA               |
//|                          INNOVATIONX INTERNATIONAL              |
//+------------------------------------------------------------------+
#property copyright "<PERSON> - InnovationX International"
#property version   "1.00"
#property description "Daily Breakout Master Strategy - Professional Trading Bot (MT5)"
#property strict

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
CTrade trade;
CPositionInfo position;

//--- Enums (must be declared before inputs)
enum ENUM_LOT_TYPE {
   LOT_MANUAL,      // Manual Lot Size
   LOT_RISK_BASED   // Risk % Based
};

enum ENUM_TP_TYPE {
   TP_FIXED_RR,     // Fixed Risk:Reward
   TP_ATR_BASED,    // ATR Based
   TP_CUSTOM_POINTS // Custom Points
};

enum ENUM_ENTRY_MODE {
   ENTRY_BODY, // Use body high/low for entry
   ENTRY_WICK  // Use wick high/low for entry
};

enum ENUM_SL_MODE {
   SL_RANGE_LOW,    // BUY SL = range low, SELL SL = range high
   SL_MID_RANGE,    // SL at middle of range
   SL_PERCENT,      // Use percentage of range
   SL_POINTS        // Use points from entry
};

//--- Input Parameters
input string SECTION_1 = "--- Lot Size & Risk ---";
input group "=== LOT SIZE & RISK ==="
input ENUM_LOT_TYPE LotSizeType = LOT_RISK_BASED; // Lot Size Type
input double ManualLotSize = 0.01; // Manual Lot Size
input double RiskPercentPerTrade = 1.0; // Risk % Per Trade
input double MaxRiskPerWeek = 5.0; // Max Risk % Per Week

input string SECTION_2 = "--- Trade Settings ---";
input group "=== TRADE SETTINGS ==="
input int MaxDailyTrades = 1; // Max Trades Per Day (Each Direction)
input double MaxSpread = 3.0; // Max Spread (Pips)
input int Slippage = 2; // Max Slippage (Pips)
int MagicNumber = 12345; // Magic Number (non-const for auto-generation)
input bool AutoMagicNumber = true; // Auto-generate magic number per symbol+timeframe+account

input string TradeComment = "DailyBreakout"; // Trade Comment

input string SECTION_3 = "--- Take Profit Settings ---";
input group "=== TAKE PROFIT SETTINGS ==="
input ENUM_TP_TYPE TakeProfitType = TP_CUSTOM_POINTS; // Take Profit Type (Default: 1000 points)
input double FixedRiskReward = 2.0; // Fixed Risk:Reward Ratio
input double ATRMultiplier = 2.0; // ATR Multiplier for TP
input int ATRPeriod = 14; // ATR Period
input double CustomTPPoints = 1000.0; // Custom TP (Points) - Default 1000 for indices/gold/BTC
input bool EnableBreakEven = true; // Enable Break Even
input double BreakEvenPoints = 300.0; // Break Even trigger (Points) - Default 300
input bool EnableTrailingStop = true; // Enable Trailing Stop (Default ON)
input double TrailingStopPoints = 200.0; // Trailing Stop Distance (Points) - Default 200

input string SECTION_4 = "--- Stop Loss & Protection ---";
input group "=== PROTECTION & FILTERS ==="
input bool EnableEquityGuard = true; // Enable Equity Guard
input double EquityGuardPercent = 10.0; // Stop if Equity Drops Below %
input double MaxDrawdownPercent = 15.0; // Max Drawdown %
input double MaxDailyLossPercent = 4.0; // Max Daily Loss %
input double MaxWeeklyLossPercent = 8.0; // Max Weekly Loss %
input bool EnableGapFilter = true; // Enable Gap Filter
input double MaxGapPoints = 2000.0; // Max Gap Size (Points)
input bool EnableNewsFilter = false; // Enable News Filter
input int NewsFilterMinutes = 30; // News Filter Minutes

input string SECTION_5 = "--- Time & Session Filters ---";
input group "=== TIME & SESSION FILTERS ==="
input bool EnableMondayTrading = true; // Trade on Monday
input bool EnableTuesdayTrading = true; // Trade on Tuesday
input bool EnableWednesdayTrading = true; // Trade on Wednesday
input bool EnableThursdayTrading = true; // Trade on Thursday
input bool EnableFridayTrading = true; // Trade on Friday
input int TriggerHour = 0; // Daily Candle Close Hour (Midnight broker time = 11 PM Berlin)
input int TriggerMinute = 0; // Daily Candle Close Minute (Broker Time)
input int SessionStartHour = 9; // Session Start Hour (9 AM broker time = 8 AM Berlin)
input int SessionEndHour = 18; // Session End Hour (6 PM broker time = 5 PM Berlin)
input int OrderPurgeHour = 21; // Delete all orders at this hour (9 PM broker time)
input int OrderPurgeMinute = 0; // Order purge minute (Broker Time)

input string SECTION_6 = "--- Advanced Features ---";
input group "=== ADVANCED FEATURES ==="
input ENUM_ENTRY_MODE EntryMode = ENTRY_WICK; // Default: Wick breakouts (Option A). Set ENTRY_BODY for Option B
input ENUM_SL_MODE StopLossMode = SL_RANGE_LOW; // SL: range low/high, mid-range, percent, points
input double SLPercent = 50.0;                // For SL_PERCENT: % between wick high/low
input double SLPoints = 0;                    // For SL_POINTS: points from entry
input bool EnableBreakoutConfirmation = false; // Require Candle Close Confirmation
input bool EnableTrailingStop = true; // Enable Trailing Stop (Default ON)
input double TrailingStopPoints = 200.0; // Trailing Stop Distance (Points) - Default 200
input bool EnablePartialTP = false; // Enable Partial Take Profit
input double PartialTPPercent = 50.0; // Partial TP % at 1:1

input string SECTION_7 = "--- Visuals & Dashboard ---";
input group "=== VISUAL & DASHBOARD ==="
input bool ShowDashboard = true; // Show Dashboard
input bool DrawZones = true; // Draw Price Zones
input color BuyZoneColor = clrLime; // Buy Zone Color
input color SellZoneColor = clrRed; // Sell Zone Color
input color DashboardColor = clrWhite; // Dashboard Text Color



//--- Global Variables
double g_DailyHigh, g_DailyLow, g_DailyOpen, g_DailyClose;
// Only using wick high/low (range high/low) - no body levels needed
double g_BuyStopPrice, g_SellStopPrice;
double g_BuyStopLoss, g_SellStopLoss;
double g_BuyTakeProfit, g_SellTakeProfit;
bool g_NewDayProcessed = false;
bool g_BuyTradeToday = false;
bool g_SellTradeToday = false;

// Dashboard Variables
double g_StartingBalance;
double g_DailyStartBalance;
double g_WeeklyStartBalance;
double g_MaxDailyLoss = 0;
double g_MaxDailyLossPercent = 0;
int g_TotalTrades = 0;
int g_WinningTrades = 0;
int g_DailyTrades = 0;
int g_WeeklyTrades = 0;
double g_DailyPL = 0;
double g_WeeklyPL = 0;
datetime g_LastDayCheck = 0;
datetime g_LastWeekCheck = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
   if(AutoMagicNumber) {
      // MT5 magic is set on the CTrade object; derive consistent magic like MT4
      long acc = (long)AccountInfoInteger(ACCOUNT_LOGIN);
      string sym = Symbol();
      int tf = Period();
      int h = (int)(acc % 1000000);
      for(int i=0;i<StringLen(sym);i++) h = (h*31 + StringGetChar(sym,i)) & 0x7FFFFFFF;
      h = (h*31 + tf) & 0x7FFFFFFF;
      if(h < 10000) h += 10000;
      MagicNumber = h;
   }
   trade.SetExpertMagicNumber(MagicNumber);
   trade.SetDeviationInPoints(Slippage);

   g_StartingBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   g_DailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   g_WeeklyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   g_LastDayCheck = TimeCurrent();
   g_LastWeekCheck = TimeCurrent();

   Print("=== DAILY BREAKOUT MASTER BOT MT5 ===");
   Print("Developed by: Brian Alvin Bagorogoza");
   Print("InnovationX International");
   Print("Contact: +49 1521 6294394");
   Print("Bot Initialized Successfully!");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
   // Clean up objects
   ObjectsDeleteAll(0, "DB_");
   Print("Daily Breakout Master Bot MT5 - InnovationX International - Deinitialized");
}

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
   Print("Daily Breakout EA initialized");

   // Calculate levels immediately if EA starts mid-day
   if(g_BuyStopPrice == 0 && g_SellStopPrice == 0) {
      Print("Calculating daily levels from previous day's candle...");

      // Get previous day's OHLC data
      double high[], low[], open[], close[];
      if(CopyHigh(Symbol(), PERIOD_D1, 1, 1, high) <= 0 ||
         CopyLow(Symbol(), PERIOD_D1, 1, 1, low) <= 0 ||
         CopyOpen(Symbol(), PERIOD_D1, 1, 1, open) <= 0 ||
         CopyClose(Symbol(), PERIOD_D1, 1, 1, close) <= 0) {
         Print("Error copying daily data in OnInit");
         return(INIT_FAILED);
      }

      g_DailyHigh = high[0];
      g_DailyLow = low[0];
      g_DailyOpen = open[0];
      g_DailyClose = close[0];

      // Set breakout levels
      g_BuyStopPrice = g_DailyHigh;
      g_SellStopPrice = g_DailyLow;

      // Calculate stop losses and take profits
      CalculateStopLosses();
      CalculateTakeProfits();

      // Check current price position and set trade flags accordingly
      double currentPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);

      if(currentPrice > g_BuyStopPrice) {
         // Price already broke above range high - only allow SELL trades
         g_BuyTradeToday = true; // Block buy trades
         g_SellTradeToday = false; // Allow sell trades
         Print("EA started with price ABOVE range. Only SELL trades allowed at: ", g_SellStopPrice);
      }
      else if(currentPrice < g_SellStopPrice) {
         // Price already broke below range low - only allow BUY trades
         g_BuyTradeToday = false; // Allow buy trades
         g_SellTradeToday = true; // Block sell trades
         Print("EA started with price BELOW range. Only BUY trades allowed at: ", g_BuyStopPrice);
      }
      else {
         // Price inside range - allow both trades
         g_BuyTradeToday = false; // Allow buy trades
         g_SellTradeToday = false; // Allow sell trades
         Print("EA started with price INSIDE range. Both trades allowed - BUY: ", g_BuyStopPrice, " SELL: ", g_SellStopPrice);
      }

      // Draw zones if enabled
      if(DrawZones) DrawDailyZones();

      Print("Levels calculated - Ready to trade!");
   }

   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
   // Check for order purge at 22:00 Berlin time
   CheckOrderPurge();

   // Check for new day at trigger time
   CheckNewDay();

   // Update dashboard
   if(ShowDashboard) UpdateDashboard();

   // Check for trade triggers (blocked during purge period)
   if(!IsOrderPurgePeriod()) {
      CheckTradeSignals();
   }

   // Manage existing trades
   ManageExistingTrades();

   // Update daily/weekly statistics
   UpdateStatistics();


//+------------------------------------------------------------------+
//| Check for order purge at specified broker time                  |
//+------------------------------------------------------------------+
void CheckOrderPurge() {
   static bool ordersPurgedToday = false;
   static int lastPurgeDay = -1;

   // Use broker time directly
   datetime currentTime = TimeCurrent();
   MqlDateTime dt;
   TimeToStruct(currentTime, dt);

   // Reset purge flag on new day
   if(dt.day != lastPurgeDay) {
      ordersPurgedToday = false;
      lastPurgeDay = dt.day;
   }

   // Check if it's purge time (broker time)
   if(dt.hour == OrderPurgeHour && dt.min == OrderPurgeMinute && !ordersPurgedToday) {
      PurgeAllOrders();
      ordersPurgedToday = true;
      Print("Order purge executed at ", OrderPurgeHour, ":", OrderPurgeMinute, " broker time (", currentTime, ")");
   }
}

//+------------------------------------------------------------------+
//| Check if we're in order purge period                            |
//+------------------------------------------------------------------+
bool IsOrderPurgePeriod() {
   datetime currentTime = TimeCurrent();
   MqlDateTime dt;
   TimeToStruct(currentTime, dt);
   // Between purge hour and midnight (00:00) broker time - no new trades allowed
   return (dt.hour >= OrderPurgeHour && dt.hour < 24);
}

//+------------------------------------------------------------------+
//| Purge all pending orders                                         |
//+------------------------------------------------------------------+
void PurgeAllOrders() {
   int totalOrders = OrdersTotal();
   int deletedCount = 0;

   for(int i = totalOrders - 1; i >= 0; i--) {
      ulong ticket = OrderGetTicket(i);
      if(ticket > 0) {
         if(OrderGetInteger(ORDER_MAGIC) == MagicNumber && OrderGetString(ORDER_SYMBOL) == Symbol()) {
            // Delete pending orders only (not market positions)
            ENUM_ORDER_TYPE orderType = (ENUM_ORDER_TYPE)OrderGetInteger(ORDER_TYPE);
            if(orderType == ORDER_TYPE_BUY_STOP || orderType == ORDER_TYPE_SELL_STOP ||
               orderType == ORDER_TYPE_BUY_LIMIT || orderType == ORDER_TYPE_SELL_LIMIT) {
               if(trade.OrderDelete(ticket)) {
                  deletedCount++;
                  Print("Deleted pending order: ", ticket);
               } else {
                  Print("Failed to delete order: ", ticket, " Error: ", GetLastError());
               }
            }
         }
      }
   }

   Print("Order purge complete. Deleted ", deletedCount, " pending orders");
}
}
//+------------------------------------------------------------------+
//| Check for order purge at 22:00 Berlin time                      |
//+------------------------------------------------------------------+
void CheckOrderPurge() {
   static bool ordersPurgedToday = false;
   static int lastPurgeDay = -1;

   datetime currentTime = TimeCurrent();
   MqlDateTime dt;
   TimeToStruct(currentTime, dt);

   // Reset purge flag on new day
   if(dt.day != lastPurgeDay) {
      ordersPurgedToday = false;
      lastPurgeDay = dt.day;
   }

   // Check if it's purge time (22:00 Berlin time)
   if(dt.hour == OrderPurgeHour && dt.min == OrderPurgeMinute && !ordersPurgedToday) {
      PurgeAllOrders();
      ordersPurgedToday = true;
      Print("Order purge executed at 22:00 Berlin time");
   }
}

//+------------------------------------------------------------------+
//| Check if we're in order purge period (22:00 - 00:00)           |
//+------------------------------------------------------------------+
bool IsOrderPurgePeriod() {
   MqlDateTime dt;
   TimeToStruct(TimeCurrent(), dt);
   // Between 22:00 and midnight (00:00) - no new trades allowed
   return (dt.hour >= OrderPurgeHour && dt.hour < 24);
}

//+------------------------------------------------------------------+
//| Purge all pending orders                                         |
//+------------------------------------------------------------------+
void PurgeAllOrders() {
   int totalOrders = OrdersTotal();
   int deletedCount = 0;

   for(int i = totalOrders - 1; i >= 0; i--) {
      ulong ticket = OrderGetTicket(i);
      if(ticket > 0) {
         if(OrderGetInteger(ORDER_MAGIC) == MagicNumber && OrderGetString(ORDER_SYMBOL) == Symbol()) {
            // Delete pending orders only (not market positions)
            ENUM_ORDER_TYPE orderType = (ENUM_ORDER_TYPE)OrderGetInteger(ORDER_TYPE);
            if(orderType == ORDER_TYPE_BUY_STOP || orderType == ORDER_TYPE_SELL_STOP ||
               orderType == ORDER_TYPE_BUY_LIMIT || orderType == ORDER_TYPE_SELL_LIMIT) {
               if(trade.OrderDelete(ticket)) {
                  deletedCount++;
                  Print("Deleted pending order: ", ticket);
               } else {
                  Print("Failed to delete order: ", ticket, " Error: ", GetLastError());
               }
            }
         }
      }
   }

   Print("Order purge complete. Deleted ", deletedCount, " pending orders");
}

//+------------------------------------------------------------------+
//| Check for new day and process daily levels                      |
//+------------------------------------------------------------------+
void CheckNewDay() {
   // Use broker time for daily reset
   datetime currentTime = TimeCurrent();
   MqlDateTime dt;
   TimeToStruct(currentTime, dt);

   // Check if it's trigger time and we haven't processed today (broker time)
   if(dt.hour == TriggerHour && dt.min == TriggerMinute) {
      MqlDateTime lastDt;
      TimeToStruct(g_LastDayCheck, lastDt);

      if(!g_NewDayProcessed || dt.day != lastDt.day) {
         ProcessNewDay();
         g_NewDayProcessed = true;
         g_LastDayCheck = currentTime;
         Print("New day processed at broker time: ", currentTime);
      }
   } else {
      g_NewDayProcessed = false;
   }
}

//+------------------------------------------------------------------+
//| Process new day - calculate levels and set pending orders       |
//+------------------------------------------------------------------+
void ProcessNewDay() {
   // Get IMMEDIATE previous day's OHLC (yesterday's completed candle)
   double high[], low[], open[], close[];

   if(CopyHigh(Symbol(), PERIOD_D1, 1, 1, high) <= 0 ||
      CopyLow(Symbol(), PERIOD_D1, 1, 1, low) <= 0 ||
      CopyOpen(Symbol(), PERIOD_D1, 1, 1, open) <= 0 ||
      CopyClose(Symbol(), PERIOD_D1, 1, 1, close) <= 0) {
      Print("Error copying daily data");
      return;
   }

   g_DailyHigh = high[0];
   g_DailyLow = low[0];
   g_DailyOpen = open[0];
   g_DailyClose = close[0];

   // SIMPLIFIED: Only use wick high/low (range high/low)
   // Remove all body logic - just use highest and lowest points of previous day
   g_BuyStopPrice = g_DailyHigh;   // BUY at range high breakout
   g_SellStopPrice = g_DailyLow;   // SELL at range low breakout

   // Stop loss logic
   if (StopLossMode == SL_RANGE_LOW) {
      // BUY SL = range low, SELL SL = range high
      g_BuyStopLoss = g_DailyLow;
      g_SellStopLoss = g_DailyHigh;
   } else if (StopLossMode == SL_MID_RANGE) {
      // SL at middle of range for both
      double mid = (g_DailyHigh + g_DailyLow) / 2.0;
      g_BuyStopLoss = mid;
      g_SellStopLoss = mid;
   } else if (StopLossMode == SL_PERCENT) {
      double range = g_DailyHigh - g_DailyLow;
      g_BuyStopLoss = g_DailyLow + (range * (SLPercent / 100.0));
      g_SellStopLoss = g_DailyHigh - (range * (SLPercent / 100.0));
   } else if (StopLossMode == SL_POINTS && SLPoints > 0) {
      double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
      g_BuyStopLoss = g_BuyStopPrice - (SLPoints * point);
      g_SellStopLoss = g_SellStopPrice + (SLPoints * point);
   }

   // Calculate take profits
   CalculateTakeProfits();

   // Reset daily trade flags
   g_BuyTradeToday = false;
   g_SellTradeToday = false;
   g_DailyTrades = 0;
   g_DailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   g_DailyPL = 0;

   // Draw zones if enabled
   if(DrawZones) DrawDailyZones();

   Print("New day processed. Buy Stop: ", g_BuyStopPrice, " Sell Stop: ", g_SellStopPrice);
}

//+------------------------------------------------------------------+
//| Calculate Stop Loss levels based on settings                    |
//+------------------------------------------------------------------+
void CalculateStopLosses() {
   switch(StopLossMode) {
      case SL_RANGE_LOW:
         // Default: SL at opposite side of range
         g_BuyStopLoss = g_DailyLow;   // Buy SL at range low
         g_SellStopLoss = g_DailyHigh; // Sell SL at range high
         break;

      case SL_MID_RANGE: {
         // SL at mid-range
         double midRange = (g_DailyHigh + g_DailyLow) / 2.0;
         g_BuyStopLoss = midRange;
         g_SellStopLoss = midRange;
         break;
      }

      case SL_POINTS:
         // SL at fixed points from entry
         double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
         g_BuyStopLoss = g_BuyStopPrice - (SLPoints * point);
         g_SellStopLoss = g_SellStopPrice + (SLPoints * point);
         break;
   }
}

//+------------------------------------------------------------------+
//| Calculate Take Profit levels based on settings                  |
//+------------------------------------------------------------------+
void CalculateTakeProfits() {
   double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
   double buyRisk = (g_BuyStopPrice - g_BuyStopLoss) / point;
   double sellRisk = (g_SellStopLoss - g_SellStopPrice) / point;

   switch(TakeProfitType) {
      case TP_FIXED_RR:
         g_BuyTakeProfit = g_BuyStopPrice + (buyRisk * FixedRiskReward * point);
         g_SellTakeProfit = g_SellStopPrice - (sellRisk * FixedRiskReward * point);
         break;

      case TP_ATR_BASED:
         int atrHandle = iATR(Symbol(), PERIOD_D1, ATRPeriod);
         double atrBuffer[];
         if(CopyBuffer(atrHandle, 0, 1, 1, atrBuffer) > 0) {
            double atr = atrBuffer[0];
            g_BuyTakeProfit = g_BuyStopPrice + (atr * ATRMultiplier);
            g_SellTakeProfit = g_SellStopPrice - (atr * ATRMultiplier);
         }
         break;

      case TP_CUSTOM_POINTS:
         // Direct points input (default 1000 points for indices/gold/BTC)
         g_BuyTakeProfit = g_BuyStopPrice + (CustomTPPoints * point);
         g_SellTakeProfit = g_SellStopPrice - (CustomTPPoints * point);
         break;
   }
}

//+------------------------------------------------------------------+
//| Check for trade signals and execute trades                      |
//+------------------------------------------------------------------+
void CheckTradeSignals() {
   if(!IsTradingAllowed()) return;

   // Get current candle data
   double high[], low[], close[];
   if(CopyHigh(Symbol(), PERIOD_CURRENT, 0, 1, high) <= 0 ||
      CopyLow(Symbol(), PERIOD_CURRENT, 0, 1, low) <= 0 ||
      CopyClose(Symbol(), PERIOD_CURRENT, 0, 1, close) <= 0) {
      Print("ERROR: Failed to get current candle data");
      return;
   }

   double currentHigh = high[0];
   double currentLow = low[0];
   double currentClose = close[0];

   double ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
   double bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
   double spread = (ask - bid) / SymbolInfoDouble(Symbol(), SYMBOL_POINT);

   // Check spread filter - but be more lenient in backtesting
   if(spread > MaxSpread && !MQLInfoInteger(MQL_TESTER)) return;

   // Check gap filter
   if(EnableGapFilter && CheckForGap()) return; // uses MaxGapPoints

   // Debug: Print current conditions
   static datetime lastConditionDebug = 0;
   if(TimeCurrent() - lastConditionDebug > 1800) { // Every 30 minutes
      Print("DEBUG: High=", currentHigh, " Low=", currentLow, " Close=", currentClose);
      Print("DEBUG: BuyStop=", g_BuyStopPrice, " SellStop=", g_SellStopPrice);
      Print("DEBUG: BuyTradeToday=", g_BuyTradeToday, " SellTradeToday=", g_SellTradeToday, " DailyTrades=", g_DailyTrades);
      lastConditionDebug = TimeCurrent();
   }

   // SMART ORDER PLACEMENT: Only place orders for levels not yet broken
   bool rangeHighBroken = (currentHigh >= g_BuyStopPrice);
   bool rangeLowBroken = (currentLow <= g_SellStopPrice);

   // Check buy signal - only if range high not yet broken
   if(!g_BuyTradeToday && g_DailyTrades < MaxDailyTrades && g_BuyStopPrice > 0) {
      if(rangeHighBroken) {
         // Range high broken - execute BUY immediately
         if(!EnableBreakoutConfirmation || currentClose > g_BuyStopPrice) {
            Print("DEBUG: Buy signal triggered! High=", currentHigh, " >= RangeHigh=", g_BuyStopPrice);
            ExecuteBuyTrade();
         }
      }
   }

   // Check sell signal - only if range low not yet broken
   if(!g_SellTradeToday && g_DailyTrades < MaxDailyTrades && g_SellStopPrice > 0) {
      if(rangeLowBroken) {
         // Range low broken - execute SELL immediately
         if(!EnableBreakoutConfirmation || currentClose < g_SellStopPrice) {
            Print("DEBUG: Sell signal triggered! Low=", currentLow, " <= RangeLow=", g_SellStopPrice);
            ExecuteSellTrade();
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Execute Buy Trade                                                |
//+------------------------------------------------------------------+
void ExecuteBuyTrade() {
   double riskPips = g_BuyStopPrice - g_BuyStopLoss;
   double lotSize = CalculateLotSize(riskPips);

   Print("DEBUG: Buy trade attempt - Risk pips: ", riskPips/SymbolInfoDouble(Symbol(), SYMBOL_POINT), " Lot size: ", lotSize);
   Print("DEBUG: Buy levels - Entry: ", g_BuyStopPrice, " SL: ", g_BuyStopLoss, " TP: ", g_BuyTakeProfit);

   if(lotSize > 0) {
      if(trade.Buy(lotSize, Symbol(), 0, g_BuyStopLoss, g_BuyTakeProfit, TradeComment + " Buy")) {
         g_BuyTradeToday = true;
         g_DailyTrades++;
         g_TotalTrades++;
         Print("SUCCESS: Buy trade executed. Ticket: ", trade.ResultOrder(), " Lot size: ", lotSize);
      } else {
         Print("ERROR: Buy trade failed. Error: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
         Print("DEBUG: Ask: ", SymbolInfoDouble(Symbol(), SYMBOL_ASK), " SL: ", g_BuyStopLoss, " TP: ", g_BuyTakeProfit);
      }
   } else {
      Print("ERROR: Buy trade failed - Invalid lot size: ", lotSize);
   }
}

//+------------------------------------------------------------------+
//| Execute Sell Trade                                               |
//+------------------------------------------------------------------+
void ExecuteSellTrade() {
   double riskPips = g_SellStopLoss - g_SellStopPrice;
   double lotSize = CalculateLotSize(riskPips);

   Print("DEBUG: Sell trade attempt - Risk pips: ", riskPips/SymbolInfoDouble(Symbol(), SYMBOL_POINT), " Lot size: ", lotSize);
   Print("DEBUG: Sell levels - Entry: ", g_SellStopPrice, " SL: ", g_SellStopLoss, " TP: ", g_SellTakeProfit);

   if(lotSize > 0) {
      if(trade.Sell(lotSize, Symbol(), 0, g_SellStopLoss, g_SellTakeProfit, TradeComment + " Sell")) {
         g_SellTradeToday = true;
         g_DailyTrades++;
         g_TotalTrades++;
         Print("SUCCESS: Sell trade executed. Ticket: ", trade.ResultOrder(), " Lot size: ", lotSize);
      } else {
         Print("ERROR: Sell trade failed. Error: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
         Print("DEBUG: Bid: ", SymbolInfoDouble(Symbol(), SYMBOL_BID), " SL: ", g_SellStopLoss, " TP: ", g_SellTakeProfit);
      }
   } else {
      Print("ERROR: Sell trade failed - Invalid lot size: ", lotSize);
   }
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk management                     |
//+------------------------------------------------------------------+
double CalculateLotSize(double riskPips) {
   double lotSize = 0;

   switch(LotSizeType) {
      case LOT_MANUAL:
         lotSize = ManualLotSize;
         break;

      case LOT_RISK_BASED:
         double riskAmount = AccountInfoDouble(ACCOUNT_BALANCE) * RiskPercentPerTrade / 100.0;
         double tickValue = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
         double tickSize = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);
         double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);

         double pipValue = tickValue * (point / tickSize);
         lotSize = riskAmount / (riskPips * pipValue);
         break;
   }

   // Normalize lot size
   double minLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);

   lotSize = MathMax(minLot, MathMin(maxLot, NormalizeDouble(lotSize / lotStep, 0) * lotStep));

   return lotSize;
}

//+------------------------------------------------------------------+
//| Manage existing trades (trailing stop, partial TP, etc.)       |
//+------------------------------------------------------------------+
void ManageExistingTrades() {
   for(int i = PositionsTotal() - 1; i >= 0; i--) {
      if(position.SelectByIndex(i) && position.Magic() == MagicNumber) {
         // 1) Break-Even first
         if(EnableBreakEven) {
            ApplyBreakEven();
         }
         // 2) Trailing stop
         if(EnableTrailingStop) {
            ApplyTrailingStop();
         }
         // 3) Optional partial TPs
         if(EnablePartialTP) {
            CheckPartialTakeProfit();
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Apply trailing stop to profitable trades                        |
//+------------------------------------------------------------------+
void ApplyTrailingStop() {
   double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
   double trailDistance = TrailingStopPoints * point;

   if(position.PositionType() == POSITION_TYPE_BUY) {
      double bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
      double newSL = bid - trailDistance;
      if(newSL > position.StopLoss() && newSL < bid) {

//+------------------------------------------------------------------+
//| Apply Break-Even: move SL to entry when profit >= BreakEvenPoints|
//+------------------------------------------------------------------+
void ApplyBreakEven() {
   double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
   double entry = position.PriceOpen();
   double trigger = BreakEvenPoints * point;

   if(position.PositionType() == POSITION_TYPE_BUY) {
      double bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
      if(bid - entry >= trigger) {
         double newSL = entry; // exact breakeven
         if(newSL > position.StopLoss() && newSL < bid) {
            trade.PositionModify(position.Ticket(), newSL, position.TakeProfit());
         }
      }
   } else if(position.PositionType() == POSITION_TYPE_SELL) {
      double ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
      if(entry - ask >= trigger) {
         double newSL = entry; // exact breakeven
         if(newSL < position.StopLoss() && newSL > ask) {
            trade.PositionModify(position.Ticket(), newSL, position.TakeProfit());
         }
      }
   }
}

         trade.PositionModify(position.Ticket(), newSL, position.TakeProfit());
      }
   } else if(position.PositionType() == POSITION_TYPE_SELL) {
      double ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
      double newSL = ask + trailDistance;
      if(newSL < position.StopLoss() && newSL > ask) {
         trade.PositionModify(position.Ticket(), newSL, position.TakeProfit());
      }
   }
}

//+------------------------------------------------------------------+
//| Check and execute partial take profit                           |
//+------------------------------------------------------------------+
void CheckPartialTakeProfit() {
   double profit = position.Profit();
   double risk = MathAbs(position.PriceOpen() - position.StopLoss()) * position.Volume() *
                 SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);

   if(profit >= risk) { // At 1:1 RR
      double partialLots = position.Volume() * PartialTPPercent / 100.0;
      if(partialLots >= SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN)) {
         trade.PositionClosePartial(position.Ticket(), partialLots);
      }
   }
}

//+------------------------------------------------------------------+
//| Check if trading is allowed based on filters                    |
//+------------------------------------------------------------------+
bool IsTradingAllowed() {
   // Check equity guard
   if(EnableEquityGuard) {
      double currentEquity = AccountInfoDouble(ACCOUNT_EQUITY);
      double equityThreshold = g_StartingBalance * (100 - EquityGuardPercent) / 100.0;
      if(currentEquity < equityThreshold) return false;
   }

   // Check max drawdown
   double drawdown = (g_StartingBalance - AccountInfoDouble(ACCOUNT_EQUITY)) / g_StartingBalance * 100.0;
   if(drawdown > MaxDrawdownPercent) return false;

   // Check day of week
   MqlDateTime dt;
   TimeToStruct(TimeCurrent(), dt);
   switch(dt.day_of_week) {
      case 1: if(!EnableMondayTrading) return false; break;
      case 2: if(!EnableTuesdayTrading) return false; break;
      case 3: if(!EnableWednesdayTrading) return false; break;
      case 4: if(!EnableThursdayTrading) return false; break;
      case 5: if(!EnableFridayTrading) return false; break;
   }

   // Check if within session hours (skip in backtesting)
   if (!MQLInfoInteger(MQL_TESTER) && !IsWithinSession()) {
      static datetime lastSessionDebug = 0;
      if(TimeCurrent() - lastSessionDebug > 3600) {
         MqlDateTime dt;
         TimeToStruct(TimeCurrent(), dt);
         Print("DEBUG: Trading blocked - Outside session hours. Current: ", dt.hour, " Session: ", SessionStartHour, "-", SessionEndHour);
         lastSessionDebug = TimeCurrent();
      }
      return false;
   }

   // Check weekly risk limit
   double weeklyRisk = MathAbs(g_WeeklyPL) / g_WeeklyStartBalance * 100.0;
   if(weeklyRisk > MaxRiskPerWeek) return false;

   return true;
}

bool IsWithinSession() {
   MqlDateTime dt;
   TimeToStruct(TimeCurrent(), dt);
   return (dt.hour >= SessionStartHour && dt.hour < SessionEndHour);
}

//+------------------------------------------------------------------+
//| Check for gap conditions                                         |
//+------------------------------------------------------------------+
bool CheckForGap() {
   double open[], close[];
   if(CopyOpen(Symbol(), PERIOD_CURRENT, 0, 1, open) <= 0 ||
      CopyClose(Symbol(), PERIOD_CURRENT, 1, 1, close) <= 0) {
      return false;
   }

   double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
   double gapSize = MathAbs(open[0] - close[0]) / point;
   return (gapSize > MaxGapPoints);
}

//+------------------------------------------------------------------+
//| Draw daily zones on chart                                        |
//+------------------------------------------------------------------+
void DrawDailyZones() {
   // Delete existing zones
   ObjectDelete(0, "DB_BuyZone");
   ObjectDelete(0, "DB_SellZone");
   ObjectDelete(0, "DB_WickRange");

   datetime startTime = TimeCurrent();
   datetime endTime = startTime + 86400; // 24 hours

   // Draw wick range (yesterday high/low) across next day session
   ObjectCreate(0, "DB_WickRange", OBJ_RECTANGLE, 0, nextDayStart, g_DailyHigh, nextDayEnd, g_DailyLow);
   ObjectSetInteger(0, "DB_WickRange", OBJPROP_COLOR, clrGray);
   ObjectSetInteger(0, "DB_WickRange", OBJPROP_STYLE, STYLE_DOT);
   ObjectSetInteger(0, "DB_WickRange", OBJPROP_BACK, true);

   // Draw buy zone (body high)
   ObjectCreate(0, "DB_BuyZone", OBJ_HLINE, 0, 0, g_BodyHigh);
   ObjectSetInteger(0, "DB_BuyZone", OBJPROP_COLOR, BuyZoneColor);
   ObjectSetInteger(0, "DB_BuyZone", OBJPROP_STYLE, STYLE_SOLID);
   ObjectSetInteger(0, "DB_BuyZone", OBJPROP_WIDTH, 2);

   // Draw sell zone (body low)
   ObjectCreate(0, "DB_SellZone", OBJ_HLINE, 0, 0, g_BodyLow);
   ObjectSetInteger(0, "DB_SellZone", OBJPROP_COLOR, SellZoneColor);
   ObjectSetInteger(0, "DB_SellZone", OBJPROP_STYLE, STYLE_SOLID);
   ObjectSetInteger(0, "DB_SellZone", OBJPROP_WIDTH, 2);
}

//+------------------------------------------------------------------+
//| Update statistics and P&L tracking                              |
//+------------------------------------------------------------------+
void UpdateStatistics() {
   MqlDateTime currentDt, lastDayDt, lastWeekDt;
   TimeToStruct(TimeCurrent(), currentDt);
   TimeToStruct(g_LastDayCheck, lastDayDt);
   TimeToStruct(g_LastWeekCheck, lastWeekDt);

   // Check for new day
   if(currentDt.day != lastDayDt.day) {
      g_DailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
      g_DailyPL = 0;
      g_DailyTrades = 0;
      g_LastDayCheck = TimeCurrent();
   }

   // Check for new week
   if(currentDt.day_of_week == 1 && lastWeekDt.day_of_week != 1) {
      g_WeeklyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
      g_WeeklyPL = 0;
      g_WeeklyTrades = 0;
      g_LastWeekCheck = TimeCurrent();
   }

   // Calculate current P&L
   g_DailyPL = AccountInfoDouble(ACCOUNT_BALANCE) - g_DailyStartBalance;
   g_WeeklyPL = AccountInfoDouble(ACCOUNT_BALANCE) - g_WeeklyStartBalance;

   // Update max daily loss
   if(g_DailyPL < g_MaxDailyLoss) {
      g_MaxDailyLoss = g_DailyPL;
      g_MaxDailyLossPercent = g_MaxDailyLoss / g_DailyStartBalance * 100.0;
   }

   // Count winning trades
   g_WinningTrades = 0;
   for(int i = 0; i < HistoryDealsTotal(); i++) {
      ulong ticket = HistoryDealGetTicket(i);
      if(HistoryDealGetInteger(ticket, DEAL_MAGIC) == MagicNumber) {
         if(HistoryDealGetDouble(ticket, DEAL_PROFIT) > 0) g_WinningTrades++;
      }
   }
}

//+------------------------------------------------------------------+
//| Update and display dashboard                                     |
//+------------------------------------------------------------------+
void UpdateDashboard() {
   int yPos = 30;
   int xPos = 20;
   int lineHeight = 18;

   // Header with branding
   CreateLabel("DB_Header", "DAILY BREAKOUT MASTER - InnovationX International",
               xPos, yPos, clrGold);
   yPos += lineHeight;

   CreateLabel("DB_Developer", "By Brian Alvin Bagorogoza | +49 1521 6294394",
               xPos, yPos, clrSilver);
   yPos += lineHeight + 5;

   // Account Balance
   CreateLabel("DB_Balance", "Account Balance: $" + DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2),
               xPos, yPos, DashboardColor);
   yPos += lineHeight;

   // Daily P&L
   color dailyColor = (g_DailyPL >= 0) ? clrLimeGreen : clrRed;
   CreateLabel("DB_DailyPL", "Daily P&L: $" + DoubleToString(g_DailyPL, 2) +
               " (" + DoubleToString(g_DailyPL/g_DailyStartBalance*100, 2) + "%)",
               xPos, yPos, dailyColor);
   yPos += lineHeight;

   // Weekly P&L
   color weeklyColor = (g_WeeklyPL >= 0) ? clrLimeGreen : clrRed;
   CreateLabel("DB_WeeklyPL", "Weekly P&L: $" + DoubleToString(g_WeeklyPL, 2) +
               " (" + DoubleToString(g_WeeklyPL/g_WeeklyStartBalance*100, 2) + "%)",
               xPos, yPos, weeklyColor);
   yPos += lineHeight;

   // Max Daily Loss
   CreateLabel("DB_MaxLoss", "Max Daily Loss: $" + DoubleToString(g_MaxDailyLoss, 2) +
               " (" + DoubleToString(g_MaxDailyLossPercent, 2) + "%)",
               xPos, yPos, clrOrange);
   yPos += lineHeight;

   // Risk per Trade
   double riskAmount = AccountInfoDouble(ACCOUNT_BALANCE) * RiskPercentPerTrade / 100.0;
   CreateLabel("DB_RiskPerTrade", "Risk per Trade: $" + DoubleToString(riskAmount, 2) +
               " (" + DoubleToString(RiskPercentPerTrade, 1) + "%)",
               xPos, yPos, DashboardColor);
   yPos += lineHeight;

   // Max Weekly Risk
   CreateLabel("DB_WeeklyRisk", "Max Weekly Risk: " + DoubleToString(MaxRiskPerWeek, 1) + "%",
               xPos, yPos, DashboardColor);
   yPos += lineHeight;

   // Win Rate
   double winRate = (g_TotalTrades > 0) ? (double)g_WinningTrades / g_TotalTrades * 100.0 : 0;
   CreateLabel("DB_WinRate", "Win Rate: " + DoubleToString(winRate, 1) + "% (" +
               IntegerToString(g_WinningTrades) + "/" + IntegerToString(g_TotalTrades) + ")",
               xPos, yPos, DashboardColor);
   yPos += lineHeight;

   // Today's Trades
   CreateLabel("DB_DailyTrades", "Today's Trades: " + IntegerToString(g_DailyTrades) +
               "/" + IntegerToString(MaxDailyTrades),
               xPos, yPos, DashboardColor);
   yPos += lineHeight;

   // Current Levels
   CreateLabel("DB_BuyLevel", "Buy Stop: " + DoubleToString(g_BuyStopPrice, (int)SymbolInfoInteger(Symbol(), SYMBOL_DIGITS)),
               xPos, yPos, BuyZoneColor);
   yPos += lineHeight;

   CreateLabel("DB_SellLevel", "Sell Stop: " + DoubleToString(g_SellStopPrice, (int)SymbolInfoInteger(Symbol(), SYMBOL_DIGITS)),
               xPos, yPos, SellZoneColor);
}

//+------------------------------------------------------------------+
//| Create or update text label                                     |
//+------------------------------------------------------------------+
void CreateLabel(string name, string text, int x, int y, color clr) {
   if(ObjectFind(0, name) == -1) {
      ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
   }
   ObjectSetString(0, name, OBJPROP_TEXT, text);
   ObjectSetString(0, name, OBJPROP_FONT, "Arial");
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 10);
   ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
   ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
}
